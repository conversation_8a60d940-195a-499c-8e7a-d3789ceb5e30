import { computed } from 'vue'

export function useCostCalculation() {
  /**
   * 计算总金额
   */
  const calculateTotal = (data) => {
    if (!Array.isArray(data)) return 0

    return data.reduce((total, item) => {
      const amount = parseFloat(item.amount || item.total || item.zjfTotal) || 0
      return total + amount
    }, 0)
  }

  /**
   * 计算选中项金额
   */
  const calculateSelected = (selectedData) => {
    return calculateTotal(selectedData)
  }

  /**
   * 计算总工程量
   */
  const calculateQuantity = (data) => {
    if (!Array.isArray(data)) return 0

    return data.reduce((total, item) => {
      const quantity = parseFloat(item.quantity || item.quantityEdit) || 0
      return total + quantity
    }, 0)
  }

  /**
   * 计算行金额
   */
  const calculateRowAmount = (row) => {
    const quantity = parseFloat(row.quantity || row.quantityEdit) || 0
    const price = parseFloat(row.price || row.zjfPrice) || 0
    return quantity * price
  }

  /**
   * 计算综合单价
   */
  const calculateCompositePrice = (row) => {
    // 人工费 + 材料费 + 机械费 + 管理费 + 利润 + 规费 + 税金
    const rfee = parseFloat(row.rfee) || 0
    const cfee = parseFloat(row.cfee) || 0
    const jfee = parseFloat(row.jfee) || 0
    const zcfee = parseFloat(row.zcfee) || 0
    const sbfPrice = parseFloat(row.sbfPrice) || 0
    const managerFee = parseFloat(row.managerFee) || 0
    const profitFee = parseFloat(row.profitFee) || 0
    const gfPrice = parseFloat(row.gfPrice) || 0
    const sjPrice = parseFloat(row.sjPrice) || 0

    return rfee + cfee + jfee + zcfee + sbfPrice + managerFee + profitFee + gfPrice + sjPrice
  }

  /**
   * 计算综合合价
   */
  const calculateCompositeTotal = (row) => {
    const quantity = parseFloat(row.quantity || row.quantityEdit) || 0
    const price = calculateCompositePrice(row)
    return quantity * price
  }

  /**
   * 计算费用合价
   */
  const calculateFeeTotal = (row, feeType) => {
    const quantity = parseFloat(row.quantity || row.quantityEdit) || 0
    const feePrice = parseFloat(row[feeType]) || 0
    return quantity * feePrice
  }

  /**
   * 计算调整后金额（措施项目）
   */
  const calculateAdjustedAmount = (row) => {
    const baseAmount = calculateRowAmount(row)
    const coefficient = parseFloat(row.adjustmentCoefficient) || 1
    return baseAmount * coefficient
  }

  /**
   * 更新行金额
   */
  const updateRowAmount = (row) => {
    // 根据行类型计算不同的金额
    if (row.kind === '03' || row.kind === '04') {
      // 清单项或定额项
      row.zjfTotal = calculateRowAmount(row)
      row.total = calculateCompositeTotal(row)
      row.amount = row.total

      // 计算各项费用合价
      row.totalRfee = calculateFeeTotal(row, 'rfee')
      row.totalCfee = calculateFeeTotal(row, 'cfee')
      row.totalJfee = calculateFeeTotal(row, 'jfee')
      row.totalZcfee = calculateFeeTotal(row, 'zcfee')
      row.sbfTotal = calculateFeeTotal(row, 'sbfPrice')
      row.totalManagerFee = calculateFeeTotal(row, 'managerFee')
      row.totalProfitFee = calculateFeeTotal(row, 'profitFee')
      row.gfTotal = calculateFeeTotal(row, 'gfPrice')
      row.sjTotal = calculateFeeTotal(row, 'sjPrice')

    } else if (row.adjustmentCoefficient) {
      // 措施项目
      row.amount = calculateAdjustedAmount(row)
    } else {
      // 普通项目
      row.amount = calculateRowAmount(row)
    }

    // 更新综合单价
    if (!row.lockPriceFlag) {
      row.price = calculateCompositePrice(row)
    }

    return row
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  return {
    calculateTotal,
    calculateSelected,
    calculateQuantity,
    calculateRowAmount,
    calculateCompositePrice,
    calculateCompositeTotal,
    calculateFeeTotal,
    calculateAdjustedAmount,
    updateRowAmount,
    formatCurrency
  }
}
