<template>
  <div class="simple-cost-table-demo">
    <div class="demo-header">
      <h1>CostTable 组件演示</h1>
      <p>集成了分部分项工程和措施项目功能的通用表格组件</p>
      <a-button @click="goBack" type="primary">返回首页</a-button>
    </div>

    <a-tabs v-model:activeKey="activeTab" type="card">
      <!-- 分部分项工程 -->
      <a-tab-pane key="subItem" tab="分部分项工程">
        <div class="demo-section">
          <h3>分部分项工程表格</h3>
          <p>支持树形结构、单元格编辑、组价匹配等功能</p>
          
          <a-table
            :columns="subItemColumns"
            :data-source="subItemData"
            :pagination="false"
            :scroll="{ x: 1200 }"
            size="small"
            bordered
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div :style="{ paddingLeft: `${(record.level || 0) * 20}px` }">
                  {{ record.name }}
                </div>
              </template>
              <template v-else-if="column.key === 'amount'">
                <span style="color: #1890ff; font-weight: 500;">
                  {{ formatAmount(record.amount || record.total || record.zjfTotal) }}
                </span>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>

      <!-- 措施项目 -->
      <a-tab-pane key="measures" tab="措施项目">
        <div class="demo-section">
          <h3>措施项目表格</h3>
          <p>支持措施类别、调整系数、快速组价等功能</p>
          
          <a-table
            :columns="measuresColumns"
            :data-source="measuresData"
            :pagination="false"
            :scroll="{ x: 1200 }"
            size="small"
            bordered
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div :style="{ paddingLeft: `${(record.level || 0) * 20}px` }">
                  {{ record.name }}
                </div>
              </template>
              <template v-else-if="column.key === 'amount'">
                <span style="color: #52c41a; font-weight: 500;">
                  {{ formatAmount(record.amount || record.total || record.zjfTotal) }}
                </span>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>

      <!-- 预算表格 -->
      <a-tab-pane key="budget" tab="预算表格">
        <div class="demo-section">
          <h3>预算表格</h3>
          <p>基础的预算表格功能</p>
          
          <a-table
            :columns="budgetColumns"
            :data-source="budgetData"
            :pagination="false"
            :scroll="{ x: 800 }"
            size="small"
            bordered
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'amount'">
                <span style="color: #722ed1; font-weight: 500;">
                  {{ formatAmount(record.amount) }}
                </span>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 汇总信息 -->
    <div class="demo-footer">
      <a-card title="数据汇总" size="small">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="分部分项总额" 
              :value="calculateTotal(subItemData)" 
              :formatter="formatAmount"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="措施项目总额" 
              :value="calculateTotal(measuresData)" 
              :formatter="formatAmount"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="预算总额" 
              :value="calculateTotal(budgetData)" 
              :formatter="formatAmount"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="项目总计" 
              :value="calculateTotal([...subItemData, ...measuresData, ...budgetData])" 
              :formatter="formatAmount"
              :value-style="{ color: '#f5222d', fontWeight: 'bold' }"
            />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 路由管理
const router = useRouter()

// 响应式数据
const activeTab = ref('subItem')

// 返回首页
const goBack = () => {
  router.push('/')
}

// 模拟数据
const subItemData = ref([
  {
    id: '1',
    dispNo: 1,
    bdCode: '010101001',
    name: '土石方工程',
    unit: '项',
    quantityEdit: 1,
    zjfPrice: 0,
    zjfTotal: 43200,
    total: 43200,
    level: 0
  },
  {
    id: '1-1',
    dispNo: 2,
    bdCode: '010101001001',
    name: '平整场地',
    unit: 'm²',
    quantityEdit: 1000,
    zjfPrice: 5.50,
    zjfTotal: 5500,
    total: 6200,
    level: 1
  },
  {
    id: '1-2',
    dispNo: 3,
    bdCode: '010101002001',
    name: '挖土方',
    unit: 'm³',
    quantityEdit: 2000,
    zjfPrice: 15.80,
    zjfTotal: 31600,
    total: 37000,
    level: 1
  }
])

const measuresData = ref([
  {
    id: 'm1',
    dispNo: 1,
    fxCode: '011001001',
    name: '安全文明施工费',
    unit: '项',
    quantityEdit: 1,
    adjustmentCoefficient: 1.0,
    zjfPrice: 15000,
    zjfTotal: 15000,
    total: 15000,
    level: 0
  },
  {
    id: 'm1-1',
    dispNo: 2,
    fxCode: '011001001001',
    name: '安全防护设施',
    unit: '项',
    quantityEdit: 1,
    adjustmentCoefficient: 1.0,
    zjfPrice: 8000,
    zjfTotal: 8000,
    total: 8000,
    level: 1
  },
  {
    id: 'm2',
    dispNo: 3,
    fxCode: '011002001',
    name: '临时设施费',
    unit: '项',
    quantityEdit: 1,
    adjustmentCoefficient: 1.2,
    zjfPrice: 10000,
    zjfTotal: 12000,
    total: 12000,
    level: 0
  }
])

const budgetData = ref([
  {
    id: 'b1',
    dispNo: 1,
    name: '建筑工程费',
    unit: '项',
    quantity: 1,
    price: 500000,
    amount: 500000
  },
  {
    id: 'b2',
    dispNo: 2,
    name: '安装工程费',
    unit: '项',
    quantity: 1,
    price: 200000,
    amount: 200000
  },
  {
    id: 'b3',
    dispNo: 3,
    name: '其他费用',
    unit: '项',
    quantity: 1,
    price: 50000,
    amount: 50000
  }
])

// 列配置
const subItemColumns = [
  { title: '序号', dataIndex: 'dispNo', key: 'dispNo', width: 60, align: 'center' },
  { title: '项目编码', dataIndex: 'bdCode', key: 'bdCode', width: 170 },
  { title: '项目名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80, align: 'center' },
  { title: '工程量', dataIndex: 'quantityEdit', key: 'quantityEdit', width: 100, align: 'right' },
  { title: '单价', dataIndex: 'zjfPrice', key: 'zjfPrice', width: 100, align: 'right' },
  { title: '合价', dataIndex: 'total', key: 'amount', width: 120, align: 'right' }
]

const measuresColumns = [
  { title: '序号', dataIndex: 'dispNo', key: 'dispNo', width: 60, align: 'center' },
  { title: '措施编码', dataIndex: 'fxCode', key: 'fxCode', width: 170 },
  { title: '项目名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80, align: 'center' },
  { title: '工程量', dataIndex: 'quantityEdit', key: 'quantityEdit', width: 100, align: 'right' },
  { title: '调整系数', dataIndex: 'adjustmentCoefficient', key: 'adjustmentCoefficient', width: 100, align: 'right' },
  { title: '单价', dataIndex: 'zjfPrice', key: 'zjfPrice', width: 100, align: 'right' },
  { title: '合价', dataIndex: 'total', key: 'amount', width: 120, align: 'right' }
]

const budgetColumns = [
  { title: '序号', dataIndex: 'dispNo', key: 'dispNo', width: 60, align: 'center' },
  { title: '项目名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 80, align: 'center' },
  { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 100, align: 'right' },
  { title: '单价', dataIndex: 'price', key: 'price', width: 100, align: 'right' },
  { title: '金额', dataIndex: 'amount', key: 'amount', width: 120, align: 'right' }
]

// 工具方法
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const calculateTotal = (data) => {
  if (!Array.isArray(data)) return 0
  
  return data.reduce((total, item) => {
    const amount = parseFloat(item.amount || item.total || item.zjfTotal) || 0
    return total + amount
  }, 0)
}
</script>

<style scoped>
.simple-cost-table-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.demo-header p {
  margin: 0 0 16px 0;
  color: #666;
}

.demo-section {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.demo-section p {
  margin: 0 0 16px 0;
  color: #666;
}

.demo-footer {
  margin-top: 24px;
}

:deep(.ant-tabs-content-holder) {
  background: transparent;
}

:deep(.ant-tabs-tabpane) {
  padding: 0;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #e6f7ff;
}
</style>
