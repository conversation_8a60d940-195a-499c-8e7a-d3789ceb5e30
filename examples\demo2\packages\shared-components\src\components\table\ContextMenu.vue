<template>
  <div class="context-menu" v-if="visible" :style="menuStyle">
    <a-menu @click="handleMenuClick" mode="vertical" :selectable="false">
      <!-- 基础操作 -->
      <a-menu-item key="copy" :disabled="!hasSelection">
        <template #icon><CopyOutlined /></template>
        复制 (Ctrl+C)
      </a-menu-item>
      
      <a-menu-item key="paste" :disabled="!canPaste || !editable">
        <template #icon><SnippetsOutlined /></template>
        粘贴 (Ctrl+V)
      </a-menu-item>
      
      <a-menu-divider />
      
      <!-- 行操作 -->
      <a-menu-item key="insertAbove" :disabled="!editable">
        <template #icon><PlusOutlined /></template>
        在上方插入行
      </a-menu-item>
      
      <a-menu-item key="insertBelow" :disabled="!editable">
        <template #icon><PlusOutlined /></template>
        在下方插入行
      </a-menu-item>
      
      <a-menu-item key="insertChild" :disabled="!editable || !hasSelection" v-if="isTreeMode">
        <template #icon><NodeIndexOutlined /></template>
        插入子行
      </a-menu-item>
      
      <a-menu-item key="delete" :disabled="!hasSelection || !editable" danger>
        <template #icon><DeleteOutlined /></template>
        删除行 (Delete)
      </a-menu-item>
      
      <a-menu-divider />
      
      <!-- 编辑操作 -->
      <a-menu-item key="edit" :disabled="!hasSelection || !editable">
        <template #icon><EditOutlined /></template>
        编辑
      </a-menu-item>
      
      <a-menu-item key="duplicate" :disabled="!hasSelection || !editable">
        <template #icon><CopyOutlined /></template>
        复制行
      </a-menu-item>
      
      <a-menu-divider v-if="showAdvancedMenu" />
      
      <!-- 高级操作 -->
      <template v-if="showAdvancedMenu">
        <a-sub-menu key="advanced" title="高级操作">
          <template #icon><SettingOutlined /></template>
          
          <a-menu-item key="batchEdit" :disabled="!hasSelection">
            批量编辑
          </a-menu-item>
          
          <a-menu-item key="clearContent" :disabled="!hasSelection || !editable">
            清空内容
          </a-menu-item>
          
          <a-menu-item key="resetCode" :disabled="!hasSelection || !editable">
            重置编码
          </a-menu-item>
          
          <a-menu-divider />
          
          <a-menu-item key="moveUp" :disabled="!canMoveUp">
            上移
          </a-menu-item>
          
          <a-menu-item key="moveDown" :disabled="!canMoveDown">
            下移
          </a-menu-item>
          
          <a-menu-divider />
          
          <a-menu-item key="expandAll" v-if="isTreeMode">
            展开全部
          </a-menu-item>
          
          <a-menu-item key="collapseAll" v-if="isTreeMode">
            收起全部
          </a-menu-item>
        </a-sub-menu>
      </template>
      
      <!-- 业务操作 -->
      <template v-if="showBusinessMenu">
        <a-menu-divider />
        
        <a-sub-menu key="business" title="业务操作" v-if="tableType === 'subItem'">
          <template #icon><CalculatorOutlined /></template>
          
          <a-menu-item key="groupPrice" :disabled="!hasSelection">
            组价匹配
          </a-menu-item>
          
          <a-menu-item key="priceAnalysis" :disabled="!hasSelection">
            单价分析
          </a-menu-item>
          
          <a-menu-item key="quantityCalc" :disabled="!hasSelection">
            工程量计算
          </a-menu-item>
          
          <a-menu-item key="importQuota" :disabled="!editable">
            导入定额
          </a-menu-item>
        </a-sub-menu>
        
        <a-sub-menu key="measures" title="措施操作" v-if="tableType === 'measures'">
          <template #icon><ToolOutlined /></template>
          
          <a-menu-item key="quickPrice" :disabled="!hasSelection">
            快速组价
          </a-menu-item>
          
          <a-menu-item key="adjustCoeff" :disabled="!hasSelection">
            调整系数
          </a-menu-item>
          
          <a-menu-item key="measureTemplate" :disabled="!editable">
            措施模板
          </a-menu-item>
        </a-sub-menu>
      </template>
      
      <a-menu-divider />
      
      <!-- 查看操作 -->
      <a-menu-item key="viewDetail" :disabled="!hasSelection">
        <template #icon><EyeOutlined /></template>
        查看详情
      </a-menu-item>
      
      <a-menu-item key="viewHistory" :disabled="!hasSelection">
        <template #icon><HistoryOutlined /></template>
        查看历史
      </a-menu-item>
      
      <!-- 导出操作 -->
      <a-menu-divider />
      
      <a-sub-menu key="export" title="导出">
        <template #icon><ExportOutlined /></template>
        
        <a-menu-item key="exportSelected" :disabled="!hasSelection">
          导出选中行
        </a-menu-item>
        
        <a-menu-item key="exportAll">
          导出全部
        </a-menu-item>
        
        <a-menu-item key="exportTemplate">
          导出模板
        </a-menu-item>
      </a-sub-menu>
    </a-menu>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import {
  CopyOutlined,
  SnippetsOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SettingOutlined,
  CalculatorOutlined,
  ToolOutlined,
  EyeOutlined,
  HistoryOutlined,
  ExportOutlined,
  NodeIndexOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  args: {
    type: Object,
    required: true
  },
  tableType: {
    type: String,
    default: 'budget'
  },
  selectedRows: {
    type: Array,
    default: () => []
  },
  copyData: {
    type: Array,
    default: () => []
  },
  editable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['menuClick'])

// 计算属性
const visible = computed(() => !!props.args)

const menuStyle = computed(() => {
  if (!props.args) return {}
  
  return {
    position: 'fixed',
    left: `${props.args.clientX}px`,
    top: `${props.args.clientY}px`,
    zIndex: 9999,
    backgroundColor: '#fff',
    border: '1px solid #d9d9d9',
    borderRadius: '6px',
    boxShadow: '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    minWidth: '160px'
  }
})

const hasSelection = computed(() => props.selectedRows.length > 0)

const canPaste = computed(() => props.copyData.length > 0)

const isTreeMode = computed(() => {
  return props.args?.row?.hasChildren !== undefined
})

const showAdvancedMenu = computed(() => {
  return props.tableType !== 'budget'
})

const showBusinessMenu = computed(() => {
  return ['subItem', 'measures'].includes(props.tableType)
})

const canMoveUp = computed(() => {
  if (!hasSelection.value) return false
  // 检查是否可以上移
  return true // 简化实现
})

const canMoveDown = computed(() => {
  if (!hasSelection.value) return false
  // 检查是否可以下移
  return true // 简化实现
})

// 事件处理
const handleMenuClick = ({ key }) => {
  emit('menuClick', {
    key,
    args: props.args,
    selectedRows: props.selectedRows
  })
}
</script>

<style scoped>
.context-menu {
  user-select: none;
}

:deep(.ant-menu) {
  border: none;
  box-shadow: none;
}

:deep(.ant-menu-item) {
  margin: 0;
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
}

:deep(.ant-menu-item:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-menu-item-disabled) {
  color: #00000040;
}

:deep(.ant-menu-item-danger:hover) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

:deep(.ant-menu-submenu-title) {
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
}

:deep(.ant-menu-submenu-title:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-menu-item-icon) {
  margin-right: 8px;
}
</style>
