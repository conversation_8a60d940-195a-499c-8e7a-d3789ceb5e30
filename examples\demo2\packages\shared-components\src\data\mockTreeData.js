/**
 * 模拟树形结构数据
 * 用于演示CostTable组件的树形表格功能
 */

export const mockSubItemData = [
  {
    id: '1',
    dispNo: 1,
    bdCode: '010101001',
    type: '分部分项',
    name: '土石方工程',
    projectAttr: '',
    specification: '',
    unit: '项',
    quantity: 1,
    quantityEdit: 1,
    quantityExpression: '',
    lockPriceFlag: false,
    zjfPrice: 0,
    zjfTotal: 0,
    price: 0,
    total: 0,
    amount: 0,
    description: '土石方工程分部',
    kind: '00',
    level: 0,
    hasChildren: true,
    expanded: true,
    children: [
      {
        id: '1-1',
        parentId: '1',
        dispNo: 2,
        bdCode: '010101001001',
        type: '清单项',
        name: '平整场地',
        projectAttr: '机械平整',
        specification: '',
        unit: 'm²',
        quantity: 1000,
        quantityEdit: 1000,
        quantityExpression: '50*20',
        lockPriceFlag: false,
        zjfPrice: 5.50,
        zjfTotal: 5500,
        price: 6.20,
        total: 6200,
        amount: 6200,
        description: '机械平整场地',
        kind: '03',
        level: 1,
        hasChildren: true,
        expanded: false,
        rfee: 1.20,
        cfee: 0.80,
        jfee: 2.50,
        zcfee: 0,
        sbfPrice: 0,
        managerFee: 0.60,
        profitFee: 0.40,
        gfPrice: 0.50,
        sjPrice: 0.20,
        totalRfee: 1200,
        totalCfee: 800,
        totalJfee: 2500,
        totalZcfee: 0,
        sbfTotal: 0,
        totalManagerFee: 600,
        totalProfitFee: 400,
        gfTotal: 500,
        sjTotal: 200,
        children: [
          {
            id: '1-1-1',
            parentId: '1-1',
            dispNo: 3,
            bdCode: '010101001001001',
            type: '定额项',
            name: '推土机推土',
            projectAttr: '',
            specification: 'T140',
            unit: 'm³',
            quantity: 500,
            quantityEdit: 500,
            quantityExpression: '1000*0.5',
            lockPriceFlag: false,
            zjfPrice: 8.50,
            zjfTotal: 4250,
            price: 8.50,
            total: 4250,
            amount: 4250,
            description: '推土机推土作业',
            kind: '04',
            level: 2,
            hasChildren: false,
            rfee: 2.00,
            cfee: 1.50,
            jfee: 4.00,
            zcfee: 0,
            sbfPrice: 0,
            managerFee: 0.60,
            profitFee: 0.40,
            gfPrice: 0,
            sjPrice: 0,
            totalRfee: 1000,
            totalCfee: 750,
            totalJfee: 2000,
            totalZcfee: 0,
            sbfTotal: 0,
            totalManagerFee: 300,
            totalProfitFee: 200,
            gfTotal: 0,
            sjTotal: 0
          },
          {
            id: '1-1-2',
            parentId: '1-1',
            dispNo: 4,
            bdCode: '010101001001002',
            type: '定额项',
            name: '人工平整',
            projectAttr: '',
            specification: '',
            unit: 'm²',
            quantity: 500,
            quantityEdit: 500,
            quantityExpression: '1000*0.5',
            lockPriceFlag: false,
            zjfPrice: 4.20,
            zjfTotal: 2100,
            price: 4.20,
            total: 2100,
            amount: 2100,
            description: '人工平整场地',
            kind: '04',
            level: 2,
            hasChildren: false,
            rfee: 3.50,
            cfee: 0.50,
            jfee: 0,
            zcfee: 0,
            sbfPrice: 0,
            managerFee: 0.15,
            profitFee: 0.05,
            gfPrice: 0,
            sjPrice: 0,
            totalRfee: 1750,
            totalCfee: 250,
            totalJfee: 0,
            totalZcfee: 0,
            sbfTotal: 0,
            totalManagerFee: 75,
            totalProfitFee: 25,
            gfTotal: 0,
            sjTotal: 0
          }
        ]
      },
      {
        id: '1-2',
        parentId: '1',
        dispNo: 5,
        bdCode: '010101002001',
        type: '清单项',
        name: '挖土方',
        projectAttr: '机械挖土',
        specification: '',
        unit: 'm³',
        quantity: 2000,
        quantityEdit: 2000,
        quantityExpression: '40*25*2',
        lockPriceFlag: false,
        zjfPrice: 15.80,
        zjfTotal: 31600,
        price: 18.50,
        total: 37000,
        amount: 37000,
        description: '机械挖土方',
        kind: '03',
        level: 1,
        hasChildren: false,
        rfee: 2.50,
        cfee: 1.20,
        jfee: 12.10,
        zcfee: 0,
        sbfPrice: 0,
        managerFee: 1.80,
        profitFee: 0.70,
        gfPrice: 0.20,
        sjPrice: 0,
        totalRfee: 5000,
        totalCfee: 2400,
        totalJfee: 24200,
        totalZcfee: 0,
        sbfTotal: 0,
        totalManagerFee: 3600,
        totalProfitFee: 1400,
        gfTotal: 400,
        sjTotal: 0
      }
    ]
  },
  {
    id: '2',
    dispNo: 6,
    bdCode: '010102001',
    type: '分部分项',
    name: '地基与基础工程',
    projectAttr: '',
    specification: '',
    unit: '项',
    quantity: 1,
    quantityEdit: 1,
    quantityExpression: '',
    lockPriceFlag: false,
    zjfPrice: 0,
    zjfTotal: 0,
    price: 0,
    total: 0,
    amount: 0,
    description: '地基与基础工程分部',
    kind: '00',
    level: 0,
    hasChildren: true,
    expanded: false,
    children: [
      {
        id: '2-1',
        parentId: '2',
        dispNo: 7,
        bdCode: '010102001001',
        type: '清单项',
        name: '混凝土基础',
        projectAttr: 'C30混凝土',
        specification: '',
        unit: 'm³',
        quantity: 150,
        quantityEdit: 150,
        quantityExpression: '10*5*3',
        lockPriceFlag: false,
        zjfPrice: 380.50,
        zjfTotal: 57075,
        price: 420.80,
        total: 63120,
        amount: 63120,
        description: 'C30混凝土基础',
        kind: '03',
        level: 1,
        hasChildren: false,
        rfee: 45.20,
        cfee: 280.50,
        jfee: 35.80,
        zcfee: 0,
        sbfPrice: 0,
        managerFee: 38.50,
        profitFee: 15.80,
        gfPrice: 5.00,
        sjPrice: 0,
        totalRfee: 6780,
        totalCfee: 42075,
        totalJfee: 5370,
        totalZcfee: 0,
        sbfTotal: 0,
        totalManagerFee: 5775,
        totalProfitFee: 2370,
        gfTotal: 750,
        sjTotal: 0
      }
    ]
  }
]

export const mockMeasuresData = [
  {
    id: 'm1',
    dispNo: 1,
    fxCode: '011001001',
    type: '措施项目',
    name: '安全文明施工费',
    projectAttr: '',
    specification: '',
    unit: '项',
    quantity: 1,
    quantityEdit: 1,
    quantityExpression: '',
    adjustmentCoefficient: 1.0,
    lockPriceFlag: false,
    zjfPrice: 15000,
    zjfTotal: 15000,
    price: 15000,
    total: 15000,
    amount: 15000,
    description: '安全文明施工费用',
    kind: '01',
    level: 0,
    hasChildren: true,
    expanded: true,
    itemCategory: '通用项目',
    constructionMeasureType: 1,
    children: [
      {
        id: 'm1-1',
        parentId: 'm1',
        dispNo: 2,
        fxCode: '011001001001',
        type: '安全防护',
        name: '安全防护设施',
        projectAttr: '',
        specification: '',
        unit: '项',
        quantity: 1,
        quantityEdit: 1,
        quantityExpression: '',
        adjustmentCoefficient: 1.0,
        lockPriceFlag: false,
        zjfPrice: 8000,
        zjfTotal: 8000,
        price: 8000,
        total: 8000,
        amount: 8000,
        description: '安全防护设施费用',
        kind: '03',
        level: 1,
        hasChildren: false,
        itemCategory: '通用项目'
      },
      {
        id: 'm1-2',
        parentId: 'm1',
        dispNo: 3,
        fxCode: '011001001002',
        type: '文明施工',
        name: '文明施工措施',
        projectAttr: '',
        specification: '',
        unit: '项',
        quantity: 1,
        quantityEdit: 1,
        quantityExpression: '',
        adjustmentCoefficient: 1.0,
        lockPriceFlag: false,
        zjfPrice: 7000,
        zjfTotal: 7000,
        price: 7000,
        total: 7000,
        amount: 7000,
        description: '文明施工措施费用',
        kind: '03',
        level: 1,
        hasChildren: false,
        itemCategory: '通用项目'
      }
    ]
  },
  {
    id: 'm2',
    dispNo: 4,
    fxCode: '011002001',
    type: '措施项目',
    name: '临时设施费',
    projectAttr: '',
    specification: '',
    unit: '项',
    quantity: 1,
    quantityEdit: 1,
    quantityExpression: '',
    adjustmentCoefficient: 1.2,
    lockPriceFlag: false,
    zjfPrice: 10000,
    zjfTotal: 12000,
    price: 12000,
    total: 12000,
    amount: 12000,
    description: '临时设施费用',
    kind: '01',
    level: 0,
    hasChildren: false,
    itemCategory: '专业项目',
    constructionMeasureType: 2
  }
]

export const mockBudgetData = [
  {
    id: 'b1',
    dispNo: 1,
    name: '建筑工程费',
    unit: '项',
    quantity: 1,
    price: 500000,
    amount: 500000,
    remark: '建筑工程总费用'
  },
  {
    id: 'b2',
    dispNo: 2,
    name: '安装工程费',
    unit: '项',
    quantity: 1,
    price: 200000,
    amount: 200000,
    remark: '安装工程总费用'
  },
  {
    id: 'b3',
    dispNo: 3,
    name: '其他费用',
    unit: '项',
    quantity: 1,
    price: 50000,
    amount: 50000,
    remark: '其他相关费用'
  }
]
