# CostTable 组件

一个功能强大的成本表格组件，集成了分部分项工程和措施项目的所有功能，支持树形结构、单元格编辑、批量操作等高级特性。

## 功能特性

### 核心功能
- ✅ 树形表格结构支持
- ✅ 单元格内联编辑
- ✅ 多种编辑器类型（文本、数值、下拉、日期等）
- ✅ 行选择和批量操作
- ✅ 复制粘贴功能
- ✅ 右键菜单
- ✅ 列显示/隐藏控制
- ✅ 数据汇总计算
- ✅ 响应式设计

### 业务功能
- ✅ 分部分项工程管理
- ✅ 措施项目管理
- ✅ 工程量计算
- ✅ 费用分析
- ✅ 组价匹配
- ✅ 调整系数计算

## 基本用法

```vue
<template>
  <CostTable
    :data="tableData"
    :columns="tableColumns"
    table-type="subItem"
    :is-tree-mode="true"
    :editable="true"
    @data-change="handleDataChange"
  />
</template>

<script setup>
import CostTable from '@shared-components/components/CostTable.vue'

const tableData = ref([
  {
    id: '1',
    name: '土石方工程',
    unit: 'm³',
    quantity: 1000,
    price: 15.50,
    amount: 15500,
    children: [
      {
        id: '1-1',
        parentId: '1',
        name: '挖土方',
        unit: 'm³',
        quantity: 500,
        price: 12.80,
        amount: 6400
      }
    ]
  }
])

const tableColumns = ref([
  { title: '项目名称', field: 'name', editable: true },
  { title: '单位', field: 'unit', editable: true },
  { title: '数量', field: 'quantity', editable: true, editorType: 'number' },
  { title: '单价', field: 'price', editable: true, editorType: 'number' },
  { title: '金额', field: 'amount', align: 'right' }
])

const handleDataChange = (newData) => {
  tableData.value = newData
}
</script>
```

## Props 配置

### 数据相关
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | Array | [] | 表格数据 |
| columns | Array | [] | 列配置 |

### 表格类型
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| tableType | String | 'budget' | 表格类型：budget/subItem/measures |

### 功能开关
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| editable | Boolean | true | 是否可编辑 |
| showFormButton | Boolean | true | 显示表单按钮 |
| showSummary | Boolean | true | 显示汇总信息 |
| showAdvancedActions | Boolean | false | 显示高级操作 |
| enableRangeSelection | Boolean | false | 启用范围选择 |

### 树形表格
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| isTreeMode | Boolean | false | 是否为树形模式 |
| treeConfig | Object | {} | 树形配置 |

### 样式配置
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| tableSize | String | 'small' | 表格尺寸：small/middle/large |
| rowKey | String | 'id' | 行键字段 |
| scrollConfig | Object | {x:1200,y:400} | 滚动配置 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| dataChange | newData | 数据变化 |
| rowSelect | selectedRows | 行选择变化 |
| cellEdit | params | 单元格编辑 |
| addRow | row | 新增行 |
| addChildRow | {parent, child} | 新增子行 |
| deleteRow | rowKeys | 删除行 |
| copyRows | rows | 复制行 |
| pasteRows | rows | 粘贴行 |
| batchEdit | rows | 批量编辑 |
| groupPrice | rows | 组价匹配 |
| quickPrice | rows | 快速组价 |

## 列配置

### 基础配置
```javascript
{
  title: '列标题',
  field: 'fieldName',        // 字段名
  dataIndex: 'fieldName',    // 数据索引（可选，默认使用field）
  width: 120,                // 列宽
  align: 'left',             // 对齐方式：left/center/right
  fixed: 'left',             // 固定列：left/right
  editable: true,            // 是否可编辑
  required: true,            // 是否必填
  visible: true,             // 是否显示
  classType: 1               // 列类型：1-基础列，2-费用列
}
```

### 编辑器配置
```javascript
{
  editorType: 'text',        // 编辑器类型
  placeholder: '请输入',     // 占位符
  options: [],               // 下拉选项
  min: 0,                    // 最小值（数值类型）
  max: 999999,               // 最大值（数值类型）
  precision: 2,              // 小数位数（数值类型）
  customEditor: Component,   // 自定义编辑器组件
  customProps: {}            // 自定义属性
}
```

### 编辑器类型
- `text` - 文本输入框
- `textarea` - 多行文本框
- `number` - 数值输入框
- `select` - 下拉选择框
- `date` - 日期选择器
- `switch` - 开关
- `checkbox` - 复选框组
- `radio` - 单选框组
- `custom` - 自定义编辑器

## 表格类型

### 分部分项工程 (subItem)
```vue
<CostTable
  table-type="subItem"
  :is-tree-mode="true"
  :show-advanced-actions="true"
  @group-price="handleGroupPrice"
/>
```

特有功能：
- 项目编码管理
- 工程量表达式计算
- 综合单价分析
- 组价匹配
- 费用细项展示

### 措施项目 (measures)
```vue
<CostTable
  table-type="measures"
  :is-tree-mode="true"
  @quick-price="handleQuickPrice"
/>
```

特有功能：
- 措施编码管理
- 调整系数计算
- 措施类别分类
- 快速组价
- 施工组织措施

### 预算表格 (budget)
```vue
<CostTable
  table-type="budget"
  :is-tree-mode="false"
/>
```

基础功能：
- 简单的预算项目管理
- 数量单价金额计算
- 基础的增删改查

## 高级功能

### 树形结构
```javascript
// 树形配置
const treeConfig = {
  childrenField: 'children',    // 子节点字段
  hasChildField: 'hasChildren', // 是否有子节点字段
  expandField: 'expanded',      // 展开状态字段
  indentSize: 20               // 缩进大小
}

// 数据结构
const treeData = [
  {
    id: '1',
    name: '父节点',
    hasChildren: true,
    expanded: true,
    children: [
      {
        id: '1-1',
        parentId: '1',
        name: '子节点',
        level: 1
      }
    ]
  }
]
```

### 批量操作
```javascript
// 复制选中行
const copySelected = () => {
  tableRef.value.copySelected()
}

// 粘贴行
const pasteRows = () => {
  tableRef.value.pasteRows()
}

// 批量编辑
const batchEdit = (rows) => {
  // 自定义批量编辑逻辑
}
```

### 自定义编辑器
```vue
<template>
  <CostTable :columns="columns" />
</template>

<script setup>
import CustomEditor from './CustomEditor.vue'

const columns = [
  {
    title: '自定义字段',
    field: 'customField',
    editable: true,
    editorType: 'custom',
    customEditor: CustomEditor,
    customProps: {
      placeholder: '自定义占位符',
      options: []
    }
  }
]
</script>
```

## 样式定制

### CSS 变量
```css
.cost-table-container {
  --primary-color: #1890ff;
  --tree-indent: 20px;
}
```

### 主题定制
```css
/* 分部分项主题 */
.table-type-subItem {
  --primary-color: #1890ff;
}

/* 措施项目主题 */
.table-type-measures {
  --primary-color: #52c41a;
}

/* 预算表格主题 */
.table-type-budget {
  --primary-color: #722ed1;
}
```

## 最佳实践

1. **数据结构设计**：确保数据包含必要的字段（id、parentId、level等）
2. **列配置优化**：合理设置列宽、对齐方式和编辑器类型
3. **性能优化**：大数据量时启用虚拟滚动
4. **用户体验**：提供清晰的操作反馈和错误提示
5. **业务集成**：根据具体业务需求定制功能和样式

## 注意事项

- 树形模式下，确保数据包含正确的父子关系
- 编辑器类型要与字段数据类型匹配
- 大数据量时注意性能优化
- 自定义编辑器需要实现标准的编辑器接口
