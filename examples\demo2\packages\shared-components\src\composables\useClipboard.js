import { ref, computed } from 'vue'

/**
 * 剪贴板操作管理
 */
export function useClipboard() {
  const clipboardData = ref([])
  const clipboardType = ref('copy') // copy, cut

  /**
   * 复制行数据
   */
  const copyRows = (rows) => {
    if (!rows || rows.length === 0) return []
    
    // 深拷贝数据，避免引用问题
    const copiedData = rows.map(row => ({
      ...JSON.parse(JSON.stringify(row)),
      // 移除一些不应该复制的字段
      id: undefined,
      selected: false,
      isNew: false,
      isModified: false,
      createTime: undefined,
      updateTime: undefined
    }))
    
    clipboardData.value = copiedData
    clipboardType.value = 'copy'
    
    return copiedData
  }

  /**
   * 剪切行数据
   */
  const cutRows = (rows) => {
    const cutData = copyRows(rows)
    clipboardType.value = 'cut'
    return cutData
  }

  /**
   * 粘贴行数据
   */
  const pasteRows = (targetData = null) => {
    if (!canPaste.value) return []
    
    const dataToUse = targetData || clipboardData.value
    
    // 生成新的行数据
    const newRows = dataToUse.map((row, index) => ({
      ...row,
      id: Date.now() + index + Math.random(),
      dispNo: undefined, // 序号会重新生成
      isNew: true,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }))
    
    // 如果是剪切操作，清空剪贴板
    if (clipboardType.value === 'cut') {
      clearClipboard()
    }
    
    return newRows
  }

  /**
   * 清空剪贴板
   */
  const clearClipboard = () => {
    clipboardData.value = []
    clipboardType.value = 'copy'
  }

  /**
   * 是否可以粘贴
   */
  const canPaste = computed(() => {
    return clipboardData.value.length > 0
  })

  /**
   * 获取剪贴板数据数量
   */
  const clipboardCount = computed(() => {
    return clipboardData.value.length
  })

  /**
   * 是否为剪切操作
   */
  const isCutOperation = computed(() => {
    return clipboardType.value === 'cut'
  })

  /**
   * 复制到系统剪贴板
   */
  const copyToSystemClipboard = async (rows, format = 'text') => {
    if (!rows || rows.length === 0) return false
    
    try {
      let textData = ''
      
      if (format === 'text') {
        // 转换为制表符分隔的文本
        textData = convertToTabDelimited(rows)
      } else if (format === 'json') {
        // 转换为JSON格式
        textData = JSON.stringify(rows, null, 2)
      } else if (format === 'csv') {
        // 转换为CSV格式
        textData = convertToCSV(rows)
      }
      
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(textData)
        return true
      } else {
        // 降级方案
        return fallbackCopyToClipboard(textData)
      }
    } catch (error) {
      console.error('复制到剪贴板失败:', error)
      return false
    }
  }

  /**
   * 从系统剪贴板读取
   */
  const readFromSystemClipboard = async () => {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        const text = await navigator.clipboard.readText()
        return parseClipboardText(text)
      }
      return []
    } catch (error) {
      console.error('从剪贴板读取失败:', error)
      return []
    }
  }

  /**
   * 转换为制表符分隔文本
   */
  const convertToTabDelimited = (rows) => {
    if (!rows || rows.length === 0) return ''
    
    // 获取所有字段
    const fields = Object.keys(rows[0]).filter(key => 
      !['id', 'selected', 'isNew', 'isModified', 'children'].includes(key)
    )
    
    // 生成标题行
    const header = fields.join('\t')
    
    // 生成数据行
    const dataRows = rows.map(row => 
      fields.map(field => {
        const value = row[field]
        if (value === null || value === undefined) return ''
        if (typeof value === 'object') return JSON.stringify(value)
        return String(value)
      }).join('\t')
    )
    
    return [header, ...dataRows].join('\n')
  }

  /**
   * 转换为CSV格式
   */
  const convertToCSV = (rows) => {
    if (!rows || rows.length === 0) return ''
    
    const fields = Object.keys(rows[0]).filter(key => 
      !['id', 'selected', 'isNew', 'isModified', 'children'].includes(key)
    )
    
    // CSV转义函数
    const escapeCSV = (value) => {
      if (value === null || value === undefined) return ''
      const str = String(value)
      if (str.includes(',') || str.includes('"') || str.includes('\n')) {
        return `"${str.replace(/"/g, '""')}"`
      }
      return str
    }
    
    // 生成标题行
    const header = fields.map(escapeCSV).join(',')
    
    // 生成数据行
    const dataRows = rows.map(row => 
      fields.map(field => escapeCSV(row[field])).join(',')
    )
    
    return [header, ...dataRows].join('\n')
  }

  /**
   * 解析剪贴板文本
   */
  const parseClipboardText = (text) => {
    if (!text) return []
    
    try {
      // 尝试解析为JSON
      return JSON.parse(text)
    } catch {
      // 尝试解析为制表符分隔文本
      return parseTabDelimited(text)
    }
  }

  /**
   * 解析制表符分隔文本
   */
  const parseTabDelimited = (text) => {
    const lines = text.split('\n').filter(line => line.trim())
    if (lines.length < 2) return []
    
    const headers = lines[0].split('\t')
    const rows = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split('\t')
      const row = {}
      
      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })
      
      rows.push(row)
    }
    
    return rows
  }

  /**
   * 降级复制方案
   */
  const fallbackCopyToClipboard = (text) => {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    try {
      const result = document.execCommand('copy')
      document.body.removeChild(textArea)
      return result
    } catch (error) {
      document.body.removeChild(textArea)
      return false
    }
  }

  return {
    clipboardData,
    clipboardType,
    copyRows,
    cutRows,
    pasteRows,
    clearClipboard,
    canPaste,
    clipboardCount,
    isCutOperation,
    copyToSystemClipboard,
    readFromSystemClipboard
  }
}
