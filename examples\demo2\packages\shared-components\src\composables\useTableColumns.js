import { ref } from 'vue'

/**
 * 表格列配置管理
 */
export function useTableColumns() {
  
  /**
   * 格式化表格列配置
   */
  const formatTableColumns = (columns, tableType) => {
    return columns.map(col => ({
      ...col,
      key: col.dataIndex || col.field,
      dataIndex: col.dataIndex || col.field,
      sorter: col.sorter !== false,
      ellipsis: col.ellipsis !== false,
      resizable: col.resizable !== false,
      // 根据表格类型设置默认宽度
      width: col.width || getDefaultWidth(col.field || col.dataIndex, tableType)
    }))
  }

  /**
   * 根据表格类型获取默认列配置
   */
  const getColumnsByType = (tableType) => {
    switch (tableType) {
      case 'subItem':
        return getSubItemColumns()
      case 'measures':
        return getMeasuresColumns()
      default:
        return getBudgetColumns()
    }
  }

  /**
   * 分部分项工程表格列配置
   */
  const getSubItemColumns = () => {
    return [
      {
        title: '',
        field: 'checkbox',
        dataIndex: 'checkbox',
        width: 40,
        align: 'center',
        classType: 1,
        fixed: 'left',
        resizable: false
      },
      {
        title: '序号',
        field: 'dispNo',
        dataIndex: 'dispNo',
        width: 60,
        align: 'center',
        classType: 1,
        fixed: 'left',
        resizable: false
      },
      {
        title: '项目编码',
        field: 'bdCode',
        dataIndex: 'bdCode',
        align: 'left',
        headerAlign: 'center',
        width: 170,
        editRender: { autofocus: '.vxe-input--inner' },
        editable: true,
        classType: 1,
        fixed: 'left',
        required: true
      },
      {
        title: '类型',
        field: 'type',
        dataIndex: 'type',
        width: 80,
        align: 'center',
        classType: 1,
        editable: true,
        editorType: 'select'
      },
      {
        title: '项目名称',
        field: 'name',
        dataIndex: 'name',
        width: 200,
        align: 'left',
        classType: 1,
        editable: true,
        editorType: 'textarea',
        required: true
      },
      {
        title: '项目特征',
        field: 'projectAttr',
        width: 180,
        classType: 1,
        editable: true,
        editorType: 'textarea'
      },
      {
        title: '规格型号',
        field: 'specification',
        width: 120,
        classType: 1,
        editable: true
      },
      {
        title: '单位',
        field: 'unit',
        width: 65,
        classType: 1,
        editable: true,
        editorType: 'select'
      },
      {
        title: '工程量表达式',
        field: 'quantityExpression',
        width: 120,
        classType: 1,
        editable: true
      },
      {
        title: '工程量',
        field: 'quantityEdit',
        width: 100,
        classType: 1,
        editable: true,
        editorType: 'number',
        align: 'right'
      },
      {
        title: '锁定综合单价',
        field: 'lockPriceFlag',
        width: 100,
        classType: 1,
        editorType: 'switch',
        align: 'center'
      },
      {
        title: '单价',
        field: 'zjfPrice',
        width: 100,
        classType: 1,
        editable: true,
        editorType: 'number',
        align: 'right'
      },
      {
        title: '合价',
        field: 'zjfTotal',
        width: 100,
        classType: 1,
        align: 'right'
      },
      {
        title: '综合单价',
        field: 'price',
        width: 100,
        classType: 1,
        align: 'right'
      },
      {
        title: '综合合价',
        field: 'total',
        width: 100,
        classType: 1,
        align: 'right'
      },
      {
        title: '备注',
        field: 'description',
        width: 150,
        classType: 1,
        editable: true,
        editorType: 'textarea'
      }
    ]
  }

  /**
   * 措施项目表格列配置
   */
  const getMeasuresColumns = () => {
    const baseColumns = getSubItemColumns()
    
    // 修改项目编码字段
    const codeColumn = baseColumns.find(col => col.field === 'bdCode')
    if (codeColumn) {
      codeColumn.field = 'fxCode'
      codeColumn.dataIndex = 'fxCode'
      codeColumn.title = '措施编码'
    }

    // 添加措施项目特有字段
    const insertIndex = baseColumns.findIndex(col => col.field === 'quantityExpression')
    if (insertIndex !== -1) {
      baseColumns.splice(insertIndex, 0, {
        title: '调整系数',
        field: 'adjustmentCoefficient',
        width: 100,
        classType: 1,
        editable: true,
        editorType: 'number',
        align: 'right'
      })
    }

    const measureTypeIndex = baseColumns.findIndex(col => col.field === 'unit') + 1
    if (measureTypeIndex > 0) {
      baseColumns.splice(measureTypeIndex, 0, {
        title: '措施类别',
        field: 'itemCategory',
        width: 120,
        classType: 1,
        editable: true,
        editorType: 'select'
      })
    }

    return baseColumns
  }

  /**
   * 预算表格列配置
   */
  const getBudgetColumns = () => {
    return [
      {
        title: '序号',
        field: 'dispNo',
        dataIndex: 'dispNo',
        width: 60,
        align: 'center',
        fixed: 'left'
      },
      {
        title: '项目名称',
        field: 'name',
        dataIndex: 'name',
        width: 200,
        align: 'left',
        editable: true,
        required: true
      },
      {
        title: '单位',
        field: 'unit',
        width: 80,
        align: 'center',
        editable: true,
        editorType: 'select'
      },
      {
        title: '数量',
        field: 'quantity',
        width: 100,
        align: 'right',
        editable: true,
        editorType: 'number'
      },
      {
        title: '单价',
        field: 'price',
        width: 100,
        align: 'right',
        editable: true,
        editorType: 'number'
      },
      {
        title: '金额',
        field: 'amount',
        width: 120,
        align: 'right'
      },
      {
        title: '备注',
        field: 'remark',
        width: 150,
        editable: true,
        editorType: 'textarea'
      }
    ]
  }

  /**
   * 获取字段默认宽度
   */
  const getDefaultWidth = (field, tableType) => {
    const widthMap = {
      checkbox: 40,
      dispNo: 60,
      bdCode: 170,
      fxCode: 170,
      type: 80,
      name: 200,
      projectAttr: 180,
      specification: 120,
      unit: 65,
      quantityExpression: 120,
      quantityEdit: 100,
      quantity: 100,
      lockPriceFlag: 100,
      zjfPrice: 100,
      zjfTotal: 100,
      price: 100,
      total: 100,
      amount: 120,
      adjustmentCoefficient: 100,
      itemCategory: 120,
      measureType: 120,
      description: 150,
      remark: 150,
      actions: 120
    }

    return widthMap[field] || 100
  }

  /**
   * 费用细项列配置
   */
  const getFeeDetailColumns = () => {
    return [
      {
        title: '人工费单价',
        field: 'rfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '人工费合价',
        field: 'totalRfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '材料费单价',
        field: 'cfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '材料费合价',
        field: 'totalCfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '机械费单价',
        field: 'jfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '机械费合价',
        field: 'totalJfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '主材费单价',
        field: 'zcfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '主材费合价',
        field: 'totalZcfee',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '设备费单价',
        field: 'sbfPrice',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      },
      {
        title: '设备费合价',
        field: 'sbfTotal',
        classType: 2,
        visible: false,
        align: 'right',
        width: 100
      }
    ]
  }

  return {
    formatTableColumns,
    getColumnsByType,
    getSubItemColumns,
    getMeasuresColumns,
    getBudgetColumns,
    getFeeDetailColumns,
    getDefaultWidth
  }
}
