<template>
  <div class="cell-editor" :class="getEditorClass()">
    <!-- 文本输入框 -->
    <template v-if="isTextEditor()">
      <a-input
        ref="editorRef"
        v-model:value="localValue"
        :placeholder="getPlaceholder()"
        @blur="handleSave"
        @keydown="handleKeydown"
        @change="handleChange"
        size="small"
        :disabled="disabled"
      />
    </template>

    <!-- 多行文本框 -->
    <template v-else-if="isTextareaEditor()">
      <a-textarea
        ref="editorRef"
        v-model:value="localValue"
        :placeholder="getPlaceholder()"
        @blur="handleSave"
        @keydown="handleKeydown"
        @change="handleChange"
        :rows="2"
        size="small"
        :disabled="disabled"
      />
    </template>

    <!-- 数值输入框 -->
    <template v-else-if="isNumberEditor()">
      <a-input-number
        ref="editorRef"
        v-model:value="localValue"
        :placeholder="getPlaceholder()"
        @blur="handleSave"
        @keydown="handleKeydown"
        @change="handleChange"
        :precision="getPrecision()"
        :min="getMin()"
        :max="getMax()"
        :step="getStep()"
        size="small"
        style="width: 100%"
        :disabled="disabled"
      />
    </template>

    <!-- 下拉选择框 -->
    <template v-else-if="isSelectEditor()">
      <a-select
        ref="editorRef"
        v-model:value="localValue"
        :placeholder="getPlaceholder()"
        @blur="handleSave"
        @keydown="handleKeydown"
        @change="handleChange"
        size="small"
        style="width: 100%"
        :disabled="disabled"
        :options="getSelectOptions()"
        :show-search="true"
        :filter-option="filterOption"
      />
    </template>

    <!-- 日期选择器 -->
    <template v-else-if="isDateEditor()">
      <a-date-picker
        ref="editorRef"
        v-model:value="localValue"
        @blur="handleSave"
        @keydown="handleKeydown"
        @change="handleChange"
        size="small"
        style="width: 100%"
        :disabled="disabled"
        format="YYYY-MM-DD"
      />
    </template>

    <!-- 开关 -->
    <template v-else-if="isSwitchEditor()">
      <a-switch
        ref="editorRef"
        v-model:checked="localValue"
        @change="handleSwitchChange"
        size="small"
        :disabled="disabled"
      />
    </template>

    <!-- 复选框组 -->
    <template v-else-if="isCheckboxEditor()">
      <a-checkbox-group
        ref="editorRef"
        v-model:value="localValue"
        @change="handleChange"
        :options="getCheckboxOptions()"
        :disabled="disabled"
      />
    </template>

    <!-- 单选框组 -->
    <template v-else-if="isRadioEditor()">
      <a-radio-group
        ref="editorRef"
        v-model:value="localValue"
        @change="handleChange"
        :options="getRadioOptions()"
        :disabled="disabled"
        size="small"
      />
    </template>

    <!-- 自定义编辑器 -->
    <template v-else-if="isCustomEditor()">
      <component
        :is="getCustomComponent()"
        ref="editorRef"
        v-model:value="localValue"
        v-bind="getCustomProps()"
        @blur="handleSave"
        @change="handleChange"
        @save="handleSave"
        @cancel="handleCancel"
      />
    </template>

    <!-- 默认文本编辑器 -->
    <template v-else>
      <a-input
        ref="editorRef"
        v-model:value="localValue"
        :placeholder="getPlaceholder()"
        @blur="handleSave"
        @keydown="handleKeydown"
        @change="handleChange"
        size="small"
        :disabled="disabled"
      />
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'

const props = defineProps({
  column: {
    type: Object,
    required: true
  },
  row: {
    type: Object,
    required: true
  },
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  tableType: {
    type: String,
    default: 'budget'
  },
  editorRef: {
    type: Object,
    default: null
  },
  getPopupContainer: {
    type: Function,
    default: null
  },
  recordIndexs: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['save', 'close', 'update'])

// 响应式数据
const localValue = ref(props.modelValue)
const disabled = ref(false)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  localValue.value = newVal
}, { immediate: true })

// 编辑器类型判断
const isTextEditor = () => {
  return ['text', 'string'].includes(getEditorType()) || 
         ['bdCode', 'fxCode', 'specification'].includes(props.column.field)
}

const isTextareaEditor = () => {
  return getEditorType() === 'textarea' || 
         ['name', 'projectAttr', 'description', 'remark'].includes(props.column.field)
}

const isNumberEditor = () => {
  return ['number', 'currency', 'quantity'].includes(getEditorType()) ||
         ['quantity', 'price', 'amount', 'zjfPrice', 'zjfTotal'].includes(props.column.field)
}

const isSelectEditor = () => {
  return getEditorType() === 'select' ||
         ['type', 'unit', 'measureType', 'itemCategory'].includes(props.column.field)
}

const isDateEditor = () => {
  return getEditorType() === 'date' ||
         ['createTime', 'updateTime'].includes(props.column.field)
}

const isSwitchEditor = () => {
  return getEditorType() === 'switch' ||
         ['lockPriceFlag', 'ifMainQd'].includes(props.column.field)
}

const isCheckboxEditor = () => {
  return getEditorType() === 'checkbox'
}

const isRadioEditor = () => {
  return getEditorType() === 'radio'
}

const isCustomEditor = () => {
  return getEditorType() === 'custom' || props.column.customEditor
}

// 获取编辑器类型
const getEditorType = () => {
  return props.column.editorType || props.column.editRender?.name || 'text'
}

// 获取编辑器配置
const getPlaceholder = () => {
  return props.column.placeholder || `请输入${props.column.title}`
}

const getPrecision = () => {
  if (props.column.field.includes('Price') || props.column.field.includes('Total')) {
    return 2
  }
  return props.column.precision || 0
}

const getMin = () => {
  return props.column.min || 0
}

const getMax = () => {
  return props.column.max || Number.MAX_SAFE_INTEGER
}

const getStep = () => {
  return props.column.step || 1
}

// 获取选项数据
const getSelectOptions = () => {
  if (props.column.options) return props.column.options
  
  // 根据字段类型返回默认选项
  switch (props.column.field) {
    case 'type':
      return getTypeOptions()
    case 'unit':
      return getUnitOptions()
    case 'measureType':
      return getMeasureTypeOptions()
    case 'itemCategory':
      return getItemCategoryOptions()
    default:
      return []
  }
}

const getTypeOptions = () => {
  return [
    { label: '材料费', value: '材料费' },
    { label: '主材费', value: '主材费' },
    { label: '设备费', value: '设备费' },
    { label: '人工费', value: '人工费' },
    { label: '机械费', value: '机械费' }
  ]
}

const getUnitOptions = () => {
  return [
    { label: 'm', value: 'm' },
    { label: 'm²', value: 'm²' },
    { label: 'm³', value: 'm³' },
    { label: 'kg', value: 'kg' },
    { label: 't', value: 't' },
    { label: '个', value: '个' },
    { label: '套', value: '套' },
    { label: '项', value: '项' }
  ]
}

const getMeasureTypeOptions = () => {
  return [
    { label: '安全文明施工费', value: '安全文明施工费' },
    { label: '环境保护费', value: '环境保护费' },
    { label: '临时设施费', value: '临时设施费' }
  ]
}

const getItemCategoryOptions = () => {
  return [
    { label: '通用项目', value: '通用项目' },
    { label: '专业项目', value: '专业项目' }
  ]
}

const getCheckboxOptions = () => {
  return props.column.checkboxOptions || []
}

const getRadioOptions = () => {
  return props.column.radioOptions || []
}

const getCustomComponent = () => {
  return props.column.customEditor || 'a-input'
}

const getCustomProps = () => {
  return props.column.customProps || {}
}

const getEditorClass = () => {
  const classes = ['editor-content']
  classes.push(`editor-${getEditorType()}`)
  if (disabled.value) classes.push('disabled')
  return classes.join(' ')
}

// 事件处理
const handleSave = () => {
  emit('save', localValue.value)
}

const handleCancel = () => {
  localValue.value = props.modelValue
  emit('close')
}

const handleChange = (value) => {
  localValue.value = value
  emit('update', value)
}

const handleSwitchChange = (checked) => {
  localValue.value = checked
  emit('update', checked)
  emit('save', checked)
}

const handleKeydown = (event) => {
  switch (event.key) {
    case 'Enter':
      if (!event.shiftKey) {
        event.preventDefault()
        handleSave()
      }
      break
    case 'Escape':
      event.preventDefault()
      handleCancel()
      break
    case 'Tab':
      handleSave()
      break
  }
}

const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    // 自动聚焦
    if (props.editorRef && props.editorRef.focus) {
      props.editorRef.focus()
    }
  })
})
</script>

<style scoped>
.cell-editor {
  width: 100%;
  height: 100%;
}

.editor-content {
  width: 100%;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select),
:deep(.ant-date-picker) {
  border: 1px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-input:focus),
:deep(.ant-input-number:focus),
:deep(.ant-select:focus),
:deep(.ant-date-picker:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
}
</style>
