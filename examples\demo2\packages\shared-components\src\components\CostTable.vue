<template>
  <div class="cost-table-container" :class="[`table-type-${tableType}`, { 'tree-mode': isTreeMode }]">
    <!-- 工具栏 -->
    <div class="table-toolbar">
      <a-space>
        <a-button type="primary" @click="addRow" :disabled="!editable">
          <template #icon><PlusOutlined /></template>
          新增
        </a-button>
        <a-button @click="addChildRow" :disabled="!editable || !hasSelection" v-if="isTreeMode">
          <template #icon><NodeIndexOutlined /></template>
          新增子项
        </a-button>
        <a-button danger @click="deleteSelected" :disabled="!hasSelection || !editable">
          <template #icon><DeleteOutlined /></template>
          删除
        </a-button>
        <a-button @click="copySelected" :disabled="!hasSelection">
          <template #icon><CopyOutlined /></template>
          复制
        </a-button>
        <a-button @click="pasteRows" :disabled="!canPaste || !editable">
          <template #icon><SnippetsOutlined /></template>
          粘贴
        </a-button>
        <a-button @click="exportData">
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
        <a-button @click="openFormWindow" type="primary" ghost v-if="showFormButton">
          <template #icon><FormOutlined /></template>
          表单编辑
        </a-button>
        <a-dropdown v-if="showAdvancedActions">
          <a-button>
            更多操作
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu @click="handleAdvancedAction">
              <a-menu-item key="batchEdit">批量编辑</a-menu-item>
              <a-menu-item key="clearEmpty">清除空行</a-menu-item>
              <a-menu-item key="resetCode">重置编码</a-menu-item>
              <a-menu-item key="groupPrice" v-if="tableType === 'subItem'">组价匹配</a-menu-item>
              <a-menu-item key="quickPrice" v-if="tableType === 'measures'">快速组价</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
      <div class="table-search">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索..."
          @search="handleSearch"
          @change="handleSearch"
          allow-clear
        />
        <a-button @click="showColumnSetting" class="column-setting-btn">
          <template #icon><SettingOutlined /></template>
        </a-button>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="table-content">
      <STable
        ref="stableRef"
        class="s-table"
        :columns="displayColumns"
        :data-source="filteredData"
        :delay="200"
        :animateRows="false"
        :pagination="false"
        :loading="loading"
        :scroll="scrollConfig"
        :size="tableSize"
        bordered
        :row-key="rowKey"
        :custom-row="customRow"
        :custom-cell="customCell"
        :custom-header-cell="customHeaderCell"
        :rowClassName="getRowClassName"
        :rangeSelection="enableRangeSelection"
        @change="handleTableChange"
        @closeEditor="handleCloseEditor"
        @openEditor="handleOpenEditor"
        @beforeOpenEditor="handleBeforeOpenEditor"
        @cellClick="handleCellClick"
        @cellKeydown="handleCellKeydown"
        @mouseup="handleCellMouseup"
        @mousedown="handleMousedown"
      >
        <!-- 自定义头部 -->
        <template #headerCell="{ title, column }">
          <span class="custom-header" style="font-weight: bold">
            <i class="vxe-icon-edit" v-show="column.editable"></i>
            &nbsp;{{ title }}
            <CloseOutlined
              class="icon-close-s"
              @click="hideColumn(column)"
              v-if="column.closable !== false"
            />
          </span>
        </template>

        <!-- 自定义单元格内容 -->
        <template #bodyCell="{ text, record: row, index, column, key, openEditor, closeEditor }">
          <CellRenderer
            :row="row"
            :column="column"
            :index="index"
            :text="text"
            :table-type="tableType"
            :tree-mode="isTreeMode"
            :editable="editable"
            @open-editor="openEditor"
            @cell-click="handleCellClick"
            @cell-dblclick="handleCellDblClick"
            @update-row="handleUpdateRow"
          />
        </template>

        <!-- 自定义编辑器 -->
        <template #cellEditor="{ column, modelValue, save, closeEditor, record: row, editorRef, getPopupContainer, recordIndexs }">
          <CellEditor
            :column="column"
            :row="row"
            :model-value="modelValue"
            :table-type="tableType"
            :editor-ref="editorRef"
            :get-popup-container="getPopupContainer"
            :record-indexs="recordIndexs"
            @save="save"
            @close="closeEditor"
            @update="handleCellUpdate"
          />
        </template>

        <!-- 右键菜单 -->
        <template #contextmenuPopup="args">
          <ContextMenu
            :args="args"
            :table-type="tableType"
            :selected-rows="selectedRows"
            :copy-data="copyData"
            :editable="editable"
            @menu-click="handleContextMenuClick"
          />
        </template>
      </STable>
    </div>

    <!-- 表格底部汇总 -->
    <div class="table-footer" v-if="showSummary">
      <div class="summary-info">
        <a-space>
          <span>总计: ¥{{ formatAmount(summary.total) }}</span>
          <span>已选: {{ selectedRowKeys.length }} 项</span>
          <span>选中金额: ¥{{ formatAmount(summary.selected) }}</span>
          <span v-if="summary.quantity">工程量: {{ summary.quantity }}</span>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ExportOutlined,
  FormOutlined,
  CopyOutlined,
  SnippetsOutlined,
  NodeIndexOutlined,
  DownOutlined,
  SettingOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
import { useCostCalculation } from '../composables/useCostCalculation.js'
import { useTableColumns } from '../composables/useTableColumns.js'
import { useClipboard } from '../composables/useClipboard.js'
import { STable } from '../plugins/stable.js'
import CellRenderer from './table/CellRenderer.vue'
import CellEditor from './table/CellEditor.vue'
import ContextMenu from './table/ContextMenu.vue'

// 注册组件
defineOptions({
  components: {
    STable,
    CellRenderer,
    CellEditor,
    ContextMenu
  }
})

const props = defineProps({
  // 数据相关
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },

  // 表格类型配置
  tableType: {
    type: String,
    default: 'budget', // budget, estimate, settlement, subItem, measures
    validator: (value) => ['budget', 'estimate', 'settlement', 'subItem', 'measures'].includes(value)
  },

  // 功能开关
  editable: {
    type: Boolean,
    default: true
  },
  showFormButton: {
    type: Boolean,
    default: true
  },
  showSummary: {
    type: Boolean,
    default: true
  },
  showAdvancedActions: {
    type: Boolean,
    default: false
  },
  enableRangeSelection: {
    type: Boolean,
    default: false
  },

  // 树形表格配置
  isTreeMode: {
    type: Boolean,
    default: false
  },
  treeConfig: {
    type: Object,
    default: () => ({
      childrenField: 'children',
      hasChildField: 'hasChildren',
      expandField: 'expanded',
      indentSize: 20
    })
  },

  // 表格样式配置
  tableSize: {
    type: String,
    default: 'small' // small, middle, large
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  scrollConfig: {
    type: Object,
    default: () => ({ x: 1200, y: 400 })
  },

  // 业务配置
  businessConfig: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits([
  'dataChange', 'rowSelect', 'cellEdit', 'openForm', 'editRow', 'deleteRow',
  'addRow', 'addChildRow', 'copyRows', 'pasteRows', 'batchEdit', 'clearEmpty',
  'resetCode', 'groupPrice', 'quickPrice', 'columnChange', 'cellUpdate',
  'contextMenuClick', 'beforeOpenEditor', 'afterCloseEditor'
])

// 响应式数据
const searchText = ref('')
const loading = ref(false)
const selectedRowKeys = ref([])
const stableRef = ref(null)
const copyData = ref([])
const hiddenColumns = ref(new Set())

// 组合式函数
const { calculateTotal, calculateSelected, calculateQuantity } = useCostCalculation()
const { formatTableColumns, getColumnsByType } = useTableColumns()
const { copyRows, pasteRows, canPaste } = useClipboard()

// 计算属性
const filteredData = computed(() => {
  if (!searchText.value) return props.data

  return props.data.filter(item => {
    const searchLower = searchText.value.toLowerCase()
    return Object.values(item).some(value =>
      String(value).toLowerCase().includes(searchLower)
    )
  })
})

// 处理表格列配置
const displayColumns = computed(() => {
  let columns = formatTableColumns(props.columns, props.tableType)

  // 根据表格类型获取默认列
  if (props.tableType === 'subItem' || props.tableType === 'measures') {
    const defaultColumns = getColumnsByType(props.tableType)
    columns = [...defaultColumns, ...columns]
  }

  // 过滤隐藏的列
  columns = columns.filter(col => !hiddenColumns.value.has(col.field || col.dataIndex))

  return columns.map(col => ({
    ...col,
    key: col.dataIndex || col.field,
    dataIndex: col.dataIndex || col.field,
    sorter: col.sorter !== false,
    ellipsis: col.ellipsis !== false,
    closable: col.closable !== false
  }))
})

// 汇总计算
const summary = computed(() => {
  const selectedData = props.data.filter(item => selectedRowKeys.value.includes(item[props.rowKey]))
  return {
    total: calculateTotal(props.data),
    selected: calculateSelected(selectedData),
    quantity: calculateQuantity(props.data)
  }
})

const hasSelection = computed(() => selectedRowKeys.value.length > 0)
const selectedRows = computed(() =>
  props.data.filter(item => selectedRowKeys.value.includes(item[props.rowKey]))
)

// 方法
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const handleTableChange = (pagination, filters, sorter) => {
  console.log('Table changed:', pagination, filters, sorter)
}

// 创建新行的工厂函数
const createNewRow = (parentRow = null) => {
  const baseRow = {
    [props.rowKey]: Date.now() + Math.random(),
    dispNo: props.data.length + 1,
    name: '',
    unit: '',
    quantity: 0,
    price: 0,
    amount: 0,
    remark: '',
    kind: '03', // 默认为清单项
    type: '',
    specification: '',
    projectAttr: '',
    quantityExpression: '',
    lockPriceFlag: false,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }

  // 根据表格类型添加特定字段
  if (props.tableType === 'subItem') {
    Object.assign(baseRow, {
      bdCode: '',
      zjfPrice: 0,
      zjfTotal: 0,
      total: 0,
      qfCode: '',
      costMajorName: '',
      measureType: ''
    })
  } else if (props.tableType === 'measures') {
    Object.assign(baseRow, {
      fxCode: '',
      itemCategory: '',
      adjustmentCoefficient: 1,
      constructionMeasureType: 1
    })
  }

  // 树形结构处理
  if (props.isTreeMode && parentRow) {
    baseRow.parentId = parentRow[props.rowKey]
    baseRow.level = (parentRow.level || 0) + 1
  }

  return baseRow
}

const addRow = () => {
  const newRow = createNewRow()
  const newData = [...props.data, newRow]

  emit('dataChange', newData)
  emit('addRow', newRow)
  message.success('新增行成功')
}

const addChildRow = () => {
  if (!hasSelection.value) return

  const parentRow = selectedRows.value[0]
  const newRow = createNewRow(parentRow)

  // 树形结构处理
  if (props.isTreeMode) {
    if (!parentRow.children) {
      parentRow.children = []
    }
    parentRow.children.push(newRow)
    parentRow.hasChildren = true
    parentRow.expanded = true
  }

  emit('dataChange', [...props.data])
  emit('addChildRow', { parent: parentRow, child: newRow })
  message.success('新增子项成功')
}

const deleteSelected = () => {
  if (!hasSelection.value) return

  const newData = props.data.filter(item => !selectedRowKeys.value.includes(item[props.rowKey]))
  emit('dataChange', newData)
  emit('deleteRow', selectedRowKeys.value)
  selectedRowKeys.value = []
  message.success('删除成功')
}

const copySelected = () => {
  if (!hasSelection.value) return

  copyData.value = copyRows(selectedRows.value)
  message.success(`已复制 ${selectedRows.value.length} 行`)
}

const pasteRows = () => {
  if (!canPaste.value) return

  const newRows = pasteRows(copyData.value)
  const newData = [...props.data, ...newRows]

  emit('dataChange', newData)
  emit('pasteRows', newRows)
  message.success(`已粘贴 ${newRows.length} 行`)
}

const exportData = () => {
  const dataToExport = selectedRowKeys.value.length > 0
    ? props.data.filter(item => selectedRowKeys.value.includes(item[props.rowKey]))
    : props.data

  console.log('导出数据:', dataToExport)
  message.success(`已导出 ${dataToExport.length} 条数据`)
}

const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
}

const openFormWindow = () => {
  emit('openForm', {
    type: 'create',
    data: null
  })
}

// 高级操作
const handleAdvancedAction = ({ key }) => {
  switch (key) {
    case 'batchEdit':
      emit('batchEdit', selectedRows.value)
      break
    case 'clearEmpty':
      clearEmptyRows()
      break
    case 'resetCode':
      resetRowCodes()
      break
    case 'groupPrice':
      emit('groupPrice', selectedRows.value)
      break
    case 'quickPrice':
      emit('quickPrice', selectedRows.value)
      break
  }
}

const clearEmptyRows = () => {
  const newData = props.data.filter(row => {
    return row.name || row.code || row.quantity || row.price
  })
  emit('dataChange', newData)
  message.success('已清除空行')
}

const resetRowCodes = () => {
  const newData = props.data.map((row, index) => ({
    ...row,
    dispNo: index + 1
  }))
  emit('dataChange', newData)
  message.success('已重置编码')
}

// 列设置
const showColumnSetting = () => {
  // 显示列设置对话框
  message.info('列设置功能开发中...')
}

const hideColumn = (column) => {
  hiddenColumns.value.add(column.field || column.dataIndex)
  emit('columnChange', { action: 'hide', column })
}

// 表格事件处理
const handleCellClick = (params) => {
  emit('cellEdit', params)
}

const handleCellDblClick = (params) => {
  if (props.editable && params.column.editable) {
    // 双击进入编辑模式
    stableRef.value?.setActiveCell(params.row, params.column.field)
  }
}

const handleCellKeydown = (params) => {
  const { event } = params

  // 处理快捷键
  if (event.ctrlKey) {
    switch (event.key) {
      case 'c':
        copySelected()
        event.preventDefault()
        break
      case 'v':
        pasteRows()
        event.preventDefault()
        break
      case 'Delete':
        deleteSelected()
        event.preventDefault()
        break
    }
  }
}

const handleCellMouseup = () => {
  // 处理鼠标抬起事件
}

const handleMousedown = () => {
  // 处理鼠标按下事件
}

const handleCloseEditor = (params) => {
  emit('afterCloseEditor', params)
}

const handleOpenEditor = () => {
  // 处理编辑器打开
}

const handleBeforeOpenEditor = (params) => {
  emit('beforeOpenEditor', params)
  return true // 允许打开编辑器
}

const handleCellUpdate = (params) => {
  emit('cellUpdate', params)
}

const handleUpdateRow = (row) => {
  const index = props.data.findIndex(item => item[props.rowKey] === row[props.rowKey])
  if (index !== -1) {
    const newData = [...props.data]
    newData[index] = { ...newData[index], ...row }
    emit('dataChange', newData)
  }
}

const handleContextMenuClick = (params) => {
  emit('contextMenuClick', params)
}

// 表格样式和行为
const customRow = (record, index) => {
  return {
    onClick: () => {
      // 行点击事件
    },
    onDblclick: () => {
      // 行双击事件
    },
    onContextmenu: (event) => {
      // 右键菜单
      event.preventDefault()
    }
  }
}

const customCell = (record, index, column) => {
  return {
    style: getCellStyle(record, column),
    class: getCellClass(record, column)
  }
}

const customHeaderCell = (column) => {
  return {
    style: getHeaderStyle(column),
    class: getHeaderClass(column)
  }
}

const getRowClassName = (record, index) => {
  const classes = []

  if (record.disabled) classes.push('row-disabled')
  if (record.isNew) classes.push('row-new')
  if (record.isModified) classes.push('row-modified')
  if (record.hasError) classes.push('row-error')
  if (selectedRowKeys.value.includes(record[props.rowKey])) classes.push('row-selected')

  // 树形结构样式
  if (props.isTreeMode) {
    classes.push(`tree-level-${record.level || 0}`)
    if (record.hasChildren) classes.push('has-children')
    if (record.expanded) classes.push('expanded')
  }

  return classes.join(' ')
}

const getCellStyle = (record, column) => {
  const style = {}

  // 根据列类型设置样式
  if (column.align) style.textAlign = column.align
  if (column.width) style.width = `${column.width}px`

  // 根据数据状态设置样式
  if (record.disabled) style.opacity = 0.5
  if (column.field === 'amount' && record.amount < 0) style.color = 'red'

  return style
}

const getCellClass = (record, column) => {
  const classes = []

  if (column.editable) classes.push('editable-cell')
  if (column.required && !record[column.field]) classes.push('required-empty')
  if (column.classType) classes.push(`class-type-${column.classType}`)

  return classes.join(' ')
}

const getHeaderStyle = (column) => {
  const style = {}
  if (column.width) style.width = `${column.width}px`
  return style
}

const getHeaderClass = (column) => {
  const classes = []
  if (column.required) classes.push('required-column')
  if (column.sortable) classes.push('sortable-column')
  return classes.join(' ')
}

const editRow = (record) => {
  emit('editRow', record)
}

const viewDetail = (record) => {
  message.info(`查看 ${record.name} 详情`)
}

const deleteRow = (record) => {
  emit('deleteRow', record)
}

// 监听数据变化
watch(() => props.data, () => {
  // 清除无效的选中项
  selectedRowKeys.value = selectedRowKeys.value.filter(key =>
    props.data.some(item => item[props.rowKey] === key)
  )
}, { deep: true })

// 生命周期
onMounted(() => {
  // 初始化表格
  nextTick(() => {
    if (stableRef.value) {
      // 设置表格初始状态
    }
  })
})

onBeforeUnmount(() => {
  // 清理工作
})

// 暴露方法
defineExpose({
  getTableData: () => filteredData.value,
  getSelectedData: () => selectedRows.value,
  clearSelection: () => { selectedRowKeys.value = [] },
  refreshTable: () => stableRef.value?.refresh(),
  setActiveCell: (row, field) => stableRef.value?.setActiveCell(row, field),
  getTableRef: () => stableRef.value,
  addRow,
  deleteSelected,
  copySelected,
  pasteRows
})
</script>

<style scoped>
.cost-table-container {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cost-table-container.tree-mode {
  --tree-indent: 20px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  flex-shrink: 0;
}

.table-search {
  display: flex;
  gap: 8px;
  align-items: center;
}

.column-setting-btn {
  border: none;
  box-shadow: none;
}

.table-content {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  position: relative;
}

.s-table {
  border: none;
  height: 100%;
}

.table-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  flex-shrink: 0;
}

.summary-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info span {
  font-weight: 500;
  color: #666;
  margin-right: 24px;
}

.summary-info span:first-child {
  color: #1890ff;
  font-size: 16px;
}

/* 表格类型样式 */
.table-type-subItem {
  --primary-color: #1890ff;
}

.table-type-measures {
  --primary-color: #52c41a;
}

.table-type-budget {
  --primary-color: #722ed1;
}

/* 自定义头部样式 */
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.icon-close-s {
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
  color: #999;
}

.custom-header:hover .icon-close-s {
  opacity: 1;
}

.icon-close-s:hover {
  color: #ff4d4f;
}

/* 行状态样式 */
:deep(.row-disabled) {
  background-color: #f5f5f5;
  opacity: 0.6;
}

:deep(.row-new) {
  background-color: #f6ffed;
  border-left: 3px solid #52c41a;
}

:deep(.row-modified) {
  background-color: #fff7e6;
  border-left: 3px solid #fa8c16;
}

:deep(.row-error) {
  background-color: #fff2f0;
  border-left: 3px solid #ff4d4f;
}

:deep(.row-selected) {
  background-color: #e6f7ff;
}

/* 树形结构样式 */
:deep(.tree-level-0) {
  font-weight: bold;
}

:deep(.tree-level-1) {
  padding-left: calc(var(--tree-indent) * 1);
}

:deep(.tree-level-2) {
  padding-left: calc(var(--tree-indent) * 2);
}

:deep(.tree-level-3) {
  padding-left: calc(var(--tree-indent) * 3);
}

:deep(.has-children) {
  position: relative;
}

:deep(.has-children::before) {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-left-color: #666;
  transform: translateY(-50%);
  transition: transform 0.2s;
}

:deep(.expanded::before) {
  transform: translateY(-50%) rotate(90deg);
}

/* 单元格样式 */
:deep(.editable-cell) {
  cursor: pointer;
  position: relative;
}

:deep(.editable-cell:hover) {
  background-color: #f0f8ff;
}

:deep(.required-empty) {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

:deep(.class-type-1) {
  background-color: #f6ffed;
}

:deep(.class-type-2) {
  background-color: #fff7e6;
}
</style>
