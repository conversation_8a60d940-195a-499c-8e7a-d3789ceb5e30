<template>
  <div class="cost-table-demo">
    <div class="demo-header">
      <h1>CostTable 组件演示</h1>
      <p>集成了分部分项工程和措施项目功能的通用表格组件</p>
    </div>

    <a-tabs v-model:activeKey="activeTab" type="card">
      <!-- 分部分项工程 -->
      <a-tab-pane key="subItem" tab="分部分项工程">
        <div class="demo-section">
          <h3>分部分项工程表格</h3>
          <p>支持树形结构、单元格编辑、组价匹配等功能</p>
          
          <CostTable
            ref="subItemTableRef"
            :data="subItemData"
            :columns="subItemColumns"
            table-type="subItem"
            :is-tree-mode="true"
            :editable="true"
            :show-summary="true"
            :show-advanced-actions="true"
            :enable-range-selection="true"
            :tree-config="treeConfig"
            :scroll-config="{ x: 2000, y: 500 }"
            @data-change="handleSubItemDataChange"
            @cell-update="handleCellUpdate"
            @add-row="handleAddRow"
            @add-child-row="handleAddChildRow"
            @delete-row="handleDeleteRow"
            @group-price="handleGroupPrice"
            @batch-edit="handleBatchEdit"
          />
        </div>
      </a-tab-pane>

      <!-- 措施项目 -->
      <a-tab-pane key="measures" tab="措施项目">
        <div class="demo-section">
          <h3>措施项目表格</h3>
          <p>支持措施类别、调整系数、快速组价等功能</p>
          
          <CostTable
            ref="measuresTableRef"
            :data="measuresData"
            :columns="measuresColumns"
            table-type="measures"
            :is-tree-mode="true"
            :editable="true"
            :show-summary="true"
            :show-advanced-actions="true"
            :tree-config="treeConfig"
            :scroll-config="{ x: 1800, y: 500 }"
            @data-change="handleMeasuresDataChange"
            @cell-update="handleCellUpdate"
            @quick-price="handleQuickPrice"
            @batch-edit="handleBatchEdit"
          />
        </div>
      </a-tab-pane>

      <!-- 预算表格 -->
      <a-tab-pane key="budget" tab="预算表格">
        <div class="demo-section">
          <h3>预算表格</h3>
          <p>基础的预算表格功能</p>
          
          <CostTable
            ref="budgetTableRef"
            :data="budgetData"
            :columns="budgetColumns"
            table-type="budget"
            :is-tree-mode="false"
            :editable="true"
            :show-summary="true"
            :scroll-config="{ x: 1200, y: 400 }"
            @data-change="handleBudgetDataChange"
            @cell-update="handleCellUpdate"
          />
        </div>
      </a-tab-pane>

      <!-- 自定义配置 -->
      <a-tab-pane key="custom" tab="自定义配置">
        <div class="demo-section">
          <h3>自定义表格配置</h3>
          <p>演示如何通过配置实现不同的表格需求</p>
          
          <div class="config-panel">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="表格类型" size="small">
                  <a-radio-group v-model:value="customConfig.tableType" @change="updateCustomTable">
                    <a-radio value="subItem">分部分项</a-radio>
                    <a-radio value="measures">措施项目</a-radio>
                    <a-radio value="budget">预算</a-radio>
                  </a-radio-group>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="功能开关" size="small">
                  <a-checkbox v-model:checked="customConfig.isTreeMode" @change="updateCustomTable">
                    树形模式
                  </a-checkbox>
                  <br />
                  <a-checkbox v-model:checked="customConfig.editable" @change="updateCustomTable">
                    可编辑
                  </a-checkbox>
                  <br />
                  <a-checkbox v-model:checked="customConfig.showSummary" @change="updateCustomTable">
                    显示汇总
                  </a-checkbox>
                  <br />
                  <a-checkbox v-model:checked="customConfig.showAdvancedActions" @change="updateCustomTable">
                    高级操作
                  </a-checkbox>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="表格尺寸" size="small">
                  <a-radio-group v-model:value="customConfig.tableSize" @change="updateCustomTable">
                    <a-radio value="small">小</a-radio>
                    <a-radio value="middle">中</a-radio>
                    <a-radio value="large">大</a-radio>
                  </a-radio-group>
                </a-card>
              </a-col>
            </a-row>
          </div>
          
          <CostTable
            ref="customTableRef"
            :data="customData"
            :columns="customColumns"
            :table-type="customConfig.tableType"
            :is-tree-mode="customConfig.isTreeMode"
            :editable="customConfig.editable"
            :show-summary="customConfig.showSummary"
            :show-advanced-actions="customConfig.showAdvancedActions"
            :table-size="customConfig.tableSize"
            :scroll-config="{ x: 1600, y: 400 }"
            @data-change="handleCustomDataChange"
          />
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 操作日志 -->
    <div class="demo-footer">
      <a-card title="操作日志" size="small">
        <div class="log-container">
          <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">{{ log.action }}</span>
            <span class="log-detail">{{ log.detail }}</span>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CostTable, mockSubItemData, mockMeasuresData, mockBudgetData } from '@cost-app/shared-components'

// 响应式数据
const activeTab = ref('subItem')
const subItemData = ref([...mockSubItemData])
const measuresData = ref([...mockMeasuresData])
const budgetData = ref([...mockBudgetData])
const operationLogs = ref([])

// 表格引用
const subItemTableRef = ref(null)
const measuresTableRef = ref(null)
const budgetTableRef = ref(null)
const customTableRef = ref(null)

// 自定义配置
const customConfig = reactive({
  tableType: 'subItem',
  isTreeMode: true,
  editable: true,
  showSummary: true,
  showAdvancedActions: true,
  tableSize: 'small'
})

// 树形配置
const treeConfig = {
  childrenField: 'children',
  hasChildField: 'hasChildren',
  expandField: 'expanded',
  indentSize: 20
}

// 列配置
const subItemColumns = ref([
  { title: '项目编码', field: 'bdCode', width: 170, editable: true },
  { title: '项目名称', field: 'name', width: 200, editable: true },
  { title: '单位', field: 'unit', width: 80, editable: true },
  { title: '工程量', field: 'quantityEdit', width: 100, editable: true },
  { title: '单价', field: 'zjfPrice', width: 100, editable: true },
  { title: '合价', field: 'zjfTotal', width: 100 },
  { title: '备注', field: 'description', width: 150, editable: true }
])

const measuresColumns = ref([
  { title: '措施编码', field: 'fxCode', width: 170, editable: true },
  { title: '项目名称', field: 'name', width: 200, editable: true },
  { title: '单位', field: 'unit', width: 80, editable: true },
  { title: '工程量', field: 'quantityEdit', width: 100, editable: true },
  { title: '调整系数', field: 'adjustmentCoefficient', width: 100, editable: true },
  { title: '单价', field: 'zjfPrice', width: 100, editable: true },
  { title: '合价', field: 'zjfTotal', width: 100 },
  { title: '备注', field: 'description', width: 150, editable: true }
])

const budgetColumns = ref([
  { title: '项目名称', field: 'name', width: 200, editable: true },
  { title: '单位', field: 'unit', width: 80, editable: true },
  { title: '数量', field: 'quantity', width: 100, editable: true },
  { title: '单价', field: 'price', width: 100, editable: true },
  { title: '金额', field: 'amount', width: 120 },
  { title: '备注', field: 'remark', width: 150, editable: true }
])

// 自定义表格数据
const customData = computed(() => {
  switch (customConfig.tableType) {
    case 'subItem':
      return subItemData.value
    case 'measures':
      return measuresData.value
    case 'budget':
      return budgetData.value
    default:
      return []
  }
})

const customColumns = computed(() => {
  switch (customConfig.tableType) {
    case 'subItem':
      return subItemColumns.value
    case 'measures':
      return measuresColumns.value
    case 'budget':
      return budgetColumns.value
    default:
      return []
  }
})

// 事件处理
const handleSubItemDataChange = (newData) => {
  subItemData.value = newData
  addLog('数据变更', '分部分项数据已更新')
}

const handleMeasuresDataChange = (newData) => {
  measuresData.value = newData
  addLog('数据变更', '措施项目数据已更新')
}

const handleBudgetDataChange = (newData) => {
  budgetData.value = newData
  addLog('数据变更', '预算数据已更新')
}

const handleCustomDataChange = (newData) => {
  if (customConfig.tableType === 'subItem') {
    subItemData.value = newData
  } else if (customConfig.tableType === 'measures') {
    measuresData.value = newData
  } else if (customConfig.tableType === 'budget') {
    budgetData.value = newData
  }
  addLog('数据变更', '自定义表格数据已更新')
}

const handleCellUpdate = (params) => {
  addLog('单元格编辑', `更新了 ${params.column?.title || '字段'}: ${params.newValue}`)
}

const handleAddRow = (row) => {
  addLog('新增行', `新增了一行数据: ${row.name || '未命名'}`)
}

const handleAddChildRow = (params) => {
  addLog('新增子行', `为 ${params.parent.name} 新增了子项: ${params.child.name || '未命名'}`)
}

const handleDeleteRow = (rowKeys) => {
  addLog('删除行', `删除了 ${rowKeys.length} 行数据`)
}

const handleGroupPrice = (rows) => {
  addLog('组价匹配', `对 ${rows.length} 行进行组价匹配`)
  message.info('组价匹配功能开发中...')
}

const handleQuickPrice = (rows) => {
  addLog('快速组价', `对 ${rows.length} 行进行快速组价`)
  message.info('快速组价功能开发中...')
}

const handleBatchEdit = (rows) => {
  addLog('批量编辑', `批量编辑 ${rows.length} 行数据`)
  message.info('批量编辑功能开发中...')
}

const updateCustomTable = () => {
  addLog('配置变更', `表格配置已更新: ${customConfig.tableType}`)
}

const addLog = (action, detail) => {
  operationLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    detail
  })
  
  // 保持最多50条日志
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 生命周期
onMounted(() => {
  addLog('系统', 'CostTable 演示页面已加载')
})
</script>

<style scoped>
.cost-table-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.demo-header p {
  margin: 0;
  color: #666;
}

.demo-section {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.demo-section p {
  margin: 0 0 16px 0;
  color: #666;
}

.config-panel {
  margin-bottom: 24px;
}

.config-panel .ant-checkbox {
  margin-bottom: 8px;
}

.demo-footer {
  margin-top: 24px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-time {
  color: #999;
  min-width: 80px;
}

.log-action {
  color: #1890ff;
  min-width: 80px;
  font-weight: 500;
}

.log-detail {
  color: #666;
  flex: 1;
}

:deep(.ant-tabs-content-holder) {
  background: transparent;
}

:deep(.ant-tabs-tabpane) {
  padding: 0;
}
</style>
