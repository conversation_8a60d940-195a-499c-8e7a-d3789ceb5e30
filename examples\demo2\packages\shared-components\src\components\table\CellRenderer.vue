<template>
  <div class="cell-renderer" :class="getCellClass()">
    <!-- 复选框列 -->
    <template v-if="column.field === 'checkbox'">
      <a-checkbox 
        :checked="row.selected" 
        @change="handleCheckboxChange"
        :disabled="row.disabled"
      />
    </template>

    <!-- 序号列 -->
    <template v-else-if="column.field === 'dispNo'">
      <span class="row-number">{{ row.dispNo || index + 1 }}</span>
    </template>

    <!-- 类型列 -->
    <template v-else-if="column.field === 'type'">
      <a-tag :color="getTypeColor(text)" v-if="text">
        {{ text }}
      </a-tag>
      <span v-else class="empty-text">-</span>
    </template>

    <!-- 项目名称列 -->
    <template v-else-if="column.field === 'name'">
      <div class="name-cell" :style="getTreeIndentStyle()">
        <!-- 树形结构展开/收起图标 -->
        <span 
          v-if="treeMode && row.hasChildren" 
          class="tree-expand-icon"
          :class="{ expanded: row.expanded }"
          @click="toggleExpand"
        >
          <CaretRightOutlined />
        </span>
        
        <!-- 项目名称 -->
        <span 
          class="name-text" 
          :class="{ 'clickable': editable && column.editable }"
          @click="handleNameClick"
          @dblclick="handleNameDblClick"
        >
          {{ text || '-' }}
        </span>
        
        <!-- 状态指示器 -->
        <span v-if="row.status" class="status-indicator">
          <a-badge :status="getStatusType(row.status)" />
        </span>
      </div>
    </template>

    <!-- 金额列 -->
    <template v-else-if="isAmountField(column.field)">
      <span class="amount-cell" :class="getAmountClass(text)">
        {{ formatAmount(text) }}
      </span>
    </template>

    <!-- 数量列 -->
    <template v-else-if="isQuantityField(column.field)">
      <span class="quantity-cell">
        {{ formatQuantity(text) }}
      </span>
    </template>

    <!-- 单位列 -->
    <template v-else-if="column.field === 'unit'">
      <span class="unit-cell">{{ text || '-' }}</span>
    </template>

    <!-- 锁定状态列 -->
    <template v-else-if="column.field === 'lockPriceFlag'">
      <a-switch 
        :checked="!!text" 
        size="small" 
        :disabled="!editable"
        @change="handleLockChange"
      />
    </template>

    <!-- 布尔值列 -->
    <template v-else-if="isBooleanField(column.field)">
      <a-tag :color="text ? 'success' : 'default'">
        {{ text ? '是' : '否' }}
      </a-tag>
    </template>

    <!-- 日期列 -->
    <template v-else-if="isDateField(column.field)">
      <span class="date-cell">
        {{ formatDate(text) }}
      </span>
    </template>

    <!-- 操作列 -->
    <template v-else-if="column.field === 'actions'">
      <a-space size="small">
        <a-button type="link" size="small" @click="handleEdit">编辑</a-button>
        <a-button type="link" size="small" danger @click="handleDelete">删除</a-button>
      </a-space>
    </template>

    <!-- 默认文本列 -->
    <template v-else>
      <span 
        class="text-cell"
        :class="{ 'clickable': editable && column.editable }"
        @click="handleCellClick"
        @dblclick="handleCellDblClick"
        :title="text"
      >
        {{ text || '-' }}
      </span>
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  column: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  text: {
    type: [String, Number, Boolean, Object],
    default: ''
  },
  tableType: {
    type: String,
    default: 'budget'
  },
  treeMode: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'openEditor', 'cellClick', 'cellDblclick', 'updateRow'
])

// 计算属性
const getCellClass = () => {
  const classes = ['cell-content']
  
  if (props.column.align) classes.push(`align-${props.column.align}`)
  if (props.column.editable) classes.push('editable')
  if (props.row.disabled) classes.push('disabled')
  
  return classes.join(' ')
}

const getTreeIndentStyle = () => {
  if (!props.treeMode || !props.row.level) return {}
  
  return {
    paddingLeft: `${(props.row.level || 0) * 20}px`
  }
}

// 工具方法
const isAmountField = (field) => {
  return ['amount', 'total', 'price', 'zjfPrice', 'zjfTotal', 'rfee', 'cfee', 'jfee'].includes(field)
}

const isQuantityField = (field) => {
  return ['quantity', 'quantityEdit'].includes(field)
}

const isBooleanField = (field) => {
  return ['lockPriceFlag', 'ifMainQd', 'ifProvisionalEstimate'].includes(field)
}

const isDateField = (field) => {
  return ['createTime', 'updateTime'].includes(field)
}

const formatAmount = (value) => {
  if (!value && value !== 0) return '0.00'
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatQuantity = (value) => {
  if (!value && value !== 0) return '0'
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 3
  })
}

const formatDate = (value) => {
  if (!value) return '-'
  return new Date(value).toLocaleDateString('zh-CN')
}

const getTypeColor = (type) => {
  const colors = {
    '材料费': 'blue',
    '主材费': 'green',
    '设备费': 'orange',
    '人工费': 'purple',
    '机械费': 'red'
  }
  return colors[type] || 'default'
}

const getAmountClass = (value) => {
  if (!value && value !== 0) return ''
  return Number(value) < 0 ? 'negative' : 'positive'
}

const getStatusType = (status) => {
  const types = {
    'active': 'processing',
    'completed': 'success',
    'error': 'error',
    'warning': 'warning'
  }
  return types[status] || 'default'
}

// 事件处理
const handleCheckboxChange = (e) => {
  emit('updateRow', { ...props.row, selected: e.target.checked })
}

const handleLockChange = (checked) => {
  emit('updateRow', { ...props.row, lockPriceFlag: checked })
}

const toggleExpand = () => {
  emit('updateRow', { ...props.row, expanded: !props.row.expanded })
}

const handleNameClick = () => {
  if (props.editable && props.column.editable) {
    emit('cellClick', { row: props.row, column: props.column })
  }
}

const handleNameDblClick = () => {
  if (props.editable && props.column.editable) {
    emit('openEditor')
  }
}

const handleCellClick = () => {
  if (props.editable && props.column.editable) {
    emit('cellClick', { row: props.row, column: props.column })
  }
}

const handleCellDblClick = () => {
  if (props.editable && props.column.editable) {
    emit('openEditor')
  }
}

const handleEdit = () => {
  emit('cellClick', { row: props.row, column: { field: 'actions' }, action: 'edit' })
}

const handleDelete = () => {
  emit('cellClick', { row: props.row, column: { field: 'actions' }, action: 'delete' })
}
</script>

<style scoped>
.cell-renderer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.cell-content {
  width: 100%;
}

.align-left { text-align: left; }
.align-center { text-align: center; }
.align-right { text-align: right; }

.editable {
  cursor: pointer;
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tree-expand-icon {
  cursor: pointer;
  transition: transform 0.2s;
  color: #666;
}

.tree-expand-icon.expanded {
  transform: rotate(90deg);
}

.name-text.clickable:hover {
  color: #1890ff;
  text-decoration: underline;
}

.amount-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.amount-cell.positive {
  color: #52c41a;
}

.amount-cell.negative {
  color: #ff4d4f;
}

.quantity-cell {
  font-family: 'Courier New', monospace;
}

.text-cell.clickable:hover {
  background-color: #f0f8ff;
}

.empty-text {
  color: #ccc;
}

.status-indicator {
  margin-left: auto;
}
</style>
